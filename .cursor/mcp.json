{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@0.0.29"]}, "mcp-server-time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=Asia/Shanghai"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "D:/workspace/mcp-shrimp-task-manager/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}}}
name: Build and Deploy

on:
  push:
    branches: [ "master" ]
    paths-ignore:
      - 'README.md'
      - 'LICENSE'
      - 'docs/**'
      - '.vscode/**'
      - '.github/**'
      - '.gitee/**'
      - '.codeif/**'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10.x
        run_install: false

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install

    - name: Build project
      run: pnpm build

    - name: Upload via SCP
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        password: ${{ secrets.SSH_PASSWORD }}
        port: ${{ secrets.SSH_PORT }}
        source: "dist/*"
        target: ${{ secrets.TARGET_PATH }}
        rm: true
        overwrite: true

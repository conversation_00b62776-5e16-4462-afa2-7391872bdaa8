/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "business": "/business";
    "business_apartment": "/business/apartment";
    "business_apartment_access": "/business/apartment/access";
    "business_apartment_manage": "/business/apartment/manage";
    "business_apartment_monitor": "/business/apartment/monitor";
    "business_apartment_stranger": "/business/apartment/stranger";
    "business_camera": "/business/camera";
    "business_camera_manage": "/business/camera/manage";
    "business_contract": "/business/contract";
    "business_contract_manage": "/business/contract/manage";
    "business_hazard": "/business/hazard";
    "business_hazard_manage": "/business/hazard/manage";
    "business_hazard_monitor": "/business/hazard/monitor";
    "business_payment": "/business/payment";
    "business_payment_manage": "/business/payment/manage";
    "business_restaurant": "/business/restaurant";
    "business_restaurant_manage": "/business/restaurant/manage";
    "business_restaurant_monitor": "/business/restaurant/monitor";
    "business_sound": "/business/sound";
    "business_sound_corpus": "/business/sound/corpus";
    "business_sound_manage": "/business/sound/manage";
    "business_tenant": "/business/tenant";
    "business_tenant_manage": "/business/tenant/manage";
    "business_vehicle": "/business/vehicle";
    "business_vehicle_manage": "/business/vehicle/manage";
    "business_vehicle_monitor": "/business/vehicle/monitor";
    "demo": "/demo";
    "demo_demo": "/demo/demo";
    "demo_tree": "/demo/tree";
    "home": "/home";
    "iframe-page": "/iframe-page/:url";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "monitor": "/monitor";
    "monitor_cache": "/monitor/cache";
    "monitor_logininfor": "/monitor/logininfor";
    "monitor_online": "/monitor/online";
    "monitor_operlog": "/monitor/operlog";
    "social-callback": "/social-callback";
    "system": "/system";
    "system_client": "/system/client";
    "system_config": "/system/config";
    "system_dept": "/system/dept";
    "system_dict": "/system/dict";
    "system_menu": "/system/menu";
    "system_notice": "/system/notice";
    "system_oss": "/system/oss";
    "system_oss-config": "/system/oss-config";
    "system_post": "/system/post";
    "system_role": "/system/role";
    "system_tenant": "/system/tenant";
    "system_tenant-package": "/system/tenant-package";
    "system_user": "/system/user";
    "tool": "/tool";
    "tool_gen": "/tool/gen";
    "user-center": "/user-center";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "business"
    | "demo"
    | "home"
    | "iframe-page"
    | "login"
    | "monitor"
    | "social-callback"
    | "system"
    | "tool"
    | "user-center"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "iframe-page"
    | "login"
    | "social-callback"
    | "user-center"
    | "business_apartment_access"
    | "business_apartment_manage"
    | "business_apartment_monitor"
    | "business_apartment_stranger"
    | "business_camera_manage"
    | "business_contract_manage"
    | "business_hazard_manage"
    | "business_hazard_monitor"
    | "business_payment_manage"
    | "business_restaurant_manage"
    | "business_restaurant_monitor"
    | "business_sound_corpus"
    | "business_sound_manage"
    | "business_tenant_manage"
    | "business_vehicle_manage"
    | "business_vehicle_monitor"
    | "demo_demo"
    | "demo_tree"
    | "home"
    | "monitor_cache"
    | "monitor_logininfor"
    | "monitor_online"
    | "monitor_operlog"
    | "system_client"
    | "system_config"
    | "system_dept"
    | "system_dict"
    | "system_menu"
    | "system_notice"
    | "system_oss-config"
    | "system_oss"
    | "system_post"
    | "system_role"
    | "system_tenant-package"
    | "system_tenant"
    | "system_user"
    | "tool_gen"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}

import { request } from '@/service/request';

/** 获取用于记录公寓基本信息的列表 */
export function fetchGetApartmentList(params?: Api.Business.ApartmentSearchParams) {
  return request<Api.Business.ApartmentList>({
    url: '/business/apartmentInfo/list',
    method: 'get',
    params
  });
}

/** 新增用于记录公寓基本信息的 */
export function fetchCreateApartment(data: Api.Business.ApartmentOperateParams) {
  return request<boolean>({
    url: '/business/apartmentInfo',
    method: 'post',
    data
  });
}

/** 修改用于记录公寓基本信息的 */
export function fetchUpdateApartment(data: Api.Business.ApartmentOperateParams) {
  return request<boolean>({
    url: '/business/apartmentInfo',
    method: 'put',
    data
  });
}

/** 批量删除用于记录公寓基本信息的 */
export function fetchBatchDeleteApartment(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/business/apartmentInfo/${ids.join(',')}`,
    method: 'delete'
  });
}

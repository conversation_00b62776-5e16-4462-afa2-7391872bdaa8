import { request } from '@/service/request';

/** 获取用于管理摄像头基本信息的列表 */
export function fetchGetCameraList(params?: Api.Business.CameraSearchParams) {
  return request<Api.Business.CameraList>({
    url: '/business/cameraInfo/list',
    method: 'get',
    params
  });
}

/** 新增用于管理摄像头基本信息的 */
export function fetchCreateCamera(data: Api.Business.CameraOperateParams) {
  return request<boolean>({
    url: '/business/cameraInfo',
    method: 'post',
    data
  });
}

/** 修改用于管理摄像头基本信息的 */
export function fetchUpdateCamera(data: Api.Business.CameraOperateParams) {
  return request<boolean>({
    url: '/business/cameraInfo',
    method: 'put',
    data
  });
}

/** 批量删除用于管理摄像头基本信息的 */
export function fetchBatchDeleteCamera(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/business/cameraInfo/${ids.join(',')}`,
    method: 'delete'
  });
}

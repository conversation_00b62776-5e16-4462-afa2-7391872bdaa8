/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'business',
    path: '/business',
    component: 'layout.base',
    meta: {
      title: 'business',
      i18nKey: 'route.business'
    },
    children: [
      {
        name: 'business_apartment',
        path: '/business/apartment',
        meta: {
          title: 'business_apartment',
          i18nKey: 'route.business_apartment'
        },
        children: [
          {
            name: 'business_apartment_access',
            path: '/business/apartment/access',
            component: 'view.business_apartment_access',
            meta: {
              title: 'business_apartment_access',
              i18nKey: 'route.business_apartment_access'
            }
          },
          {
            name: 'business_apartment_manage',
            path: '/business/apartment/manage',
            component: 'view.business_apartment_manage',
            meta: {
              title: 'business_apartment_manage',
              i18nKey: 'route.business_apartment_manage'
            }
          },
          {
            name: 'business_apartment_monitor',
            path: '/business/apartment/monitor',
            component: 'view.business_apartment_monitor',
            meta: {
              title: 'business_apartment_monitor',
              i18nKey: 'route.business_apartment_monitor'
            }
          },
          {
            name: 'business_apartment_stranger',
            path: '/business/apartment/stranger',
            component: 'view.business_apartment_stranger',
            meta: {
              title: 'business_apartment_stranger',
              i18nKey: 'route.business_apartment_stranger'
            }
          }
        ]
      },
      {
        name: 'business_camera',
        path: '/business/camera',
        meta: {
          title: 'business_camera',
          i18nKey: 'route.business_camera'
        },
        children: [
          {
            name: 'business_camera_manage',
            path: '/business/camera/manage',
            component: 'view.business_camera_manage',
            meta: {
              title: 'business_camera_manage',
              i18nKey: 'route.business_camera_manage'
            }
          }
        ]
      },
      {
        name: 'business_contract',
        path: '/business/contract',
        meta: {
          title: 'business_contract',
          i18nKey: 'route.business_contract'
        },
        children: [
          {
            name: 'business_contract_manage',
            path: '/business/contract/manage',
            component: 'view.business_contract_manage',
            meta: {
              title: 'business_contract_manage',
              i18nKey: 'route.business_contract_manage'
            }
          }
        ]
      },
      {
        name: 'business_hazard',
        path: '/business/hazard',
        meta: {
          title: 'business_hazard',
          i18nKey: 'route.business_hazard'
        },
        children: [
          {
            name: 'business_hazard_manage',
            path: '/business/hazard/manage',
            component: 'view.business_hazard_manage',
            meta: {
              title: 'business_hazard_manage',
              i18nKey: 'route.business_hazard_manage'
            }
          },
          {
            name: 'business_hazard_monitor',
            path: '/business/hazard/monitor',
            component: 'view.business_hazard_monitor',
            meta: {
              title: 'business_hazard_monitor',
              i18nKey: 'route.business_hazard_monitor'
            }
          }
        ]
      },
      {
        name: 'business_payment',
        path: '/business/payment',
        meta: {
          title: 'business_payment',
          i18nKey: 'route.business_payment'
        },
        children: [
          {
            name: 'business_payment_manage',
            path: '/business/payment/manage',
            component: 'view.business_payment_manage',
            meta: {
              title: 'business_payment_manage',
              i18nKey: 'route.business_payment_manage'
            }
          }
        ]
      },
      {
        name: 'business_restaurant',
        path: '/business/restaurant',
        meta: {
          title: 'business_restaurant',
          i18nKey: 'route.business_restaurant'
        },
        children: [
          {
            name: 'business_restaurant_manage',
            path: '/business/restaurant/manage',
            component: 'view.business_restaurant_manage',
            meta: {
              title: 'business_restaurant_manage',
              i18nKey: 'route.business_restaurant_manage'
            }
          },
          {
            name: 'business_restaurant_monitor',
            path: '/business/restaurant/monitor',
            component: 'view.business_restaurant_monitor',
            meta: {
              title: 'business_restaurant_monitor',
              i18nKey: 'route.business_restaurant_monitor'
            }
          }
        ]
      },
      {
        name: 'business_sound',
        path: '/business/sound',
        meta: {
          title: 'business_sound',
          i18nKey: 'route.business_sound'
        },
        children: [
          {
            name: 'business_sound_corpus',
            path: '/business/sound/corpus',
            component: 'view.business_sound_corpus',
            meta: {
              title: 'business_sound_corpus',
              i18nKey: 'route.business_sound_corpus'
            }
          },
          {
            name: 'business_sound_manage',
            path: '/business/sound/manage',
            component: 'view.business_sound_manage',
            meta: {
              title: 'business_sound_manage',
              i18nKey: 'route.business_sound_manage'
            }
          }
        ]
      },
      {
        name: 'business_tenant',
        path: '/business/tenant',
        meta: {
          title: 'business_tenant',
          i18nKey: 'route.business_tenant'
        },
        children: [
          {
            name: 'business_tenant_manage',
            path: '/business/tenant/manage',
            component: 'view.business_tenant_manage',
            meta: {
              title: 'business_tenant_manage',
              i18nKey: 'route.business_tenant_manage'
            }
          }
        ]
      },
      {
        name: 'business_vehicle',
        path: '/business/vehicle',
        meta: {
          title: 'business_vehicle',
          i18nKey: 'route.business_vehicle'
        },
        children: [
          {
            name: 'business_vehicle_manage',
            path: '/business/vehicle/manage',
            component: 'view.business_vehicle_manage',
            meta: {
              title: 'business_vehicle_manage',
              i18nKey: 'route.business_vehicle_manage'
            }
          },
          {
            name: 'business_vehicle_monitor',
            path: '/business/vehicle/monitor',
            component: 'view.business_vehicle_monitor',
            meta: {
              title: 'business_vehicle_monitor',
              i18nKey: 'route.business_vehicle_monitor'
            }
          }
        ]
      }
    ]
  },
  {
    name: 'demo',
    path: '/demo',
    component: 'layout.base',
    meta: {
      title: 'demo',
      i18nKey: 'route.demo'
    },
    children: [
      {
        name: 'demo_demo',
        path: '/demo/demo',
        component: 'view.demo_demo',
        meta: {
          title: 'demo_demo',
          i18nKey: 'route.demo_demo'
        }
      },
      {
        name: 'demo_tree',
        path: '/demo/tree',
        component: 'view.demo_tree',
        meta: {
          title: 'demo_tree',
          i18nKey: 'route.demo_tree'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'monitor',
    path: '/monitor',
    component: 'layout.base',
    meta: {
      title: 'monitor',
      i18nKey: 'route.monitor'
    },
    children: [
      {
        name: 'monitor_cache',
        path: '/monitor/cache',
        component: 'view.monitor_cache',
        meta: {
          title: 'monitor_cache',
          i18nKey: 'route.monitor_cache'
        }
      },
      {
        name: 'monitor_logininfor',
        path: '/monitor/logininfor',
        component: 'view.monitor_logininfor',
        meta: {
          title: 'monitor_logininfor',
          i18nKey: 'route.monitor_logininfor'
        }
      },
      {
        name: 'monitor_online',
        path: '/monitor/online',
        component: 'view.monitor_online',
        meta: {
          title: 'monitor_online',
          i18nKey: 'route.monitor_online'
        }
      },
      {
        name: 'monitor_operlog',
        path: '/monitor/operlog',
        component: 'view.monitor_operlog',
        meta: {
          title: 'monitor_operlog',
          i18nKey: 'route.monitor_operlog'
        }
      }
    ]
  },
  {
    name: 'social-callback',
    path: '/social-callback',
    component: 'layout.blank$view.social-callback',
    meta: {
      title: 'social-callback',
      i18nKey: 'route.social-callback',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system',
      localIcon: 'menu-system',
      order: 1
    },
    children: [
      {
        name: 'system_client',
        path: '/system/client',
        component: 'view.system_client',
        meta: {
          title: 'system_client',
          i18nKey: 'route.system_client'
        }
      },
      {
        name: 'system_config',
        path: '/system/config',
        component: 'view.system_config',
        meta: {
          title: 'system_config',
          i18nKey: 'route.system_config'
        }
      },
      {
        name: 'system_dept',
        path: '/system/dept',
        component: 'view.system_dept',
        meta: {
          title: 'system_dept',
          i18nKey: 'route.system_dept'
        }
      },
      {
        name: 'system_dict',
        path: '/system/dict',
        component: 'view.system_dict',
        meta: {
          title: 'system_dict',
          i18nKey: 'route.system_dict'
        }
      },
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu',
          localIcon: 'menu-tree-table',
          order: 3
        }
      },
      {
        name: 'system_notice',
        path: '/system/notice',
        component: 'view.system_notice',
        meta: {
          title: 'system_notice',
          i18nKey: 'route.system_notice'
        }
      },
      {
        name: 'system_oss',
        path: '/system/oss',
        component: 'view.system_oss',
        meta: {
          title: 'system_oss',
          i18nKey: 'route.system_oss'
        }
      },
      {
        name: 'system_oss-config',
        path: '/system/oss-config',
        component: 'view.system_oss-config',
        meta: {
          title: 'system_oss-config',
          i18nKey: 'route.system_oss-config',
          constant: true,
          hideInMenu: true,
          icon: 'hugeicons:configuration-01'
        }
      },
      {
        name: 'system_post',
        path: '/system/post',
        component: 'view.system_post',
        meta: {
          title: 'system_post',
          i18nKey: 'route.system_post'
        }
      },
      {
        name: 'system_role',
        path: '/system/role',
        component: 'view.system_role',
        meta: {
          title: 'system_role',
          i18nKey: 'route.system_role'
        }
      },
      {
        name: 'system_tenant',
        path: '/system/tenant',
        component: 'view.system_tenant',
        meta: {
          title: 'system_tenant',
          i18nKey: 'route.system_tenant'
        }
      },
      {
        name: 'system_tenant-package',
        path: '/system/tenant-package',
        component: 'view.system_tenant-package',
        meta: {
          title: 'system_tenant-package',
          i18nKey: 'route.system_tenant-package'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  },
  {
    name: 'tool',
    path: '/tool',
    component: 'layout.base',
    meta: {
      title: 'tool',
      i18nKey: 'route.tool',
      localIcon: 'menu-tool',
      order: 4
    },
    children: [
      {
        name: 'tool_gen',
        path: '/tool/gen',
        component: 'view.tool_gen',
        meta: {
          title: 'tool_gen',
          i18nKey: 'route.tool_gen',
          localIcon: 'menu-code',
          order: 2
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      icon: 'material-symbols:account-circle-full',
      hideInMenu: true
    }
  }
];

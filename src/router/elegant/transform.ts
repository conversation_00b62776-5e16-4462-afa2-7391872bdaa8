/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteRecordRaw, RouteComponent } from 'vue-router';
import type { ElegantConstRoute } from '@elegant-router/vue';
import type { RouteMap, RouteKey, RoutePath } from '@elegant-router/types';

/**
 * transform elegant const routes to vue routes
 * @param routes elegant const routes
 * @param layouts layout components
 * @param views view components
 */
export function transformElegantRoutesToVueRoutes(
  routes: ElegantConstRoute[],
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  return routes.flatMap(route => transformElegantRouteToVueRoute(route, layouts, views));
}

/**
 * transform elegant route to vue route
 * @param route elegant const route
 * @param layouts layout components
 * @param views view components
 */
function transformElegantRouteToVueRoute(
  route: ElegantConstRoute,
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  const LAYOUT_PREFIX = 'layout.';
  const VIEW_PREFIX = 'view.';
  const ROUTE_DEGREE_SPLITTER = '_';
  const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

  function isLayout(component: string) {
    return component.startsWith(LAYOUT_PREFIX);
  }

  function getLayoutName(component: string) {
    const layout = component.replace(LAYOUT_PREFIX, '');

    if(!layouts[layout]) {
      throw new Error(`Layout component "${layout}" not found`);
    }

    return layout;
  }

  function isView(component: string) {
    return component.startsWith(VIEW_PREFIX);
  }

  function getViewName(component: string) {
    const view = component.replace(VIEW_PREFIX, '');

    if(!views[view]) {
      throw new Error(`View component "${view}" not found`);
    }

    return view;
  }

  function isFirstLevelRoute(item: ElegantConstRoute) {
    return !item.name.includes(ROUTE_DEGREE_SPLITTER);
  }

  function isSingleLevelRoute(item: ElegantConstRoute) {
    return isFirstLevelRoute(item) && !item.children?.length;
  }

  function getSingleLevelRouteComponent(component: string) {
    const [layout, view] = component.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT);

    return {
      layout: getLayoutName(layout),
      view: getViewName(view)
    };
  }

  const vueRoutes: RouteRecordRaw[] = [];

  // add props: true to route
  if (route.path.includes(':') && !route.props) {
    route.props = true;
  }

  const { name, path, component, children, ...rest } = route;

  const vueRoute = { name, path, ...rest } as RouteRecordRaw;

  try {
    if (component) {
      if (isSingleLevelRoute(route)) {
        const { layout, view } = getSingleLevelRouteComponent(component);

        const singleLevelRoute: RouteRecordRaw = {
          path,
          component: layouts[layout],
          meta: {
            title: route.meta?.title || ''
          },
          children: [
            {
              name,
              path: '',
              component: views[view],
              ...rest
            } as RouteRecordRaw
          ]
        };

        return [singleLevelRoute];
      }

      if (isLayout(component)) {
        const layoutName = getLayoutName(component);

        vueRoute.component = layouts[layoutName];
      }

      if (isView(component)) {
        const viewName = getViewName(component);

        vueRoute.component = views[viewName];
      }

    }
  } catch (error: any) {
    console.error(`Error transforming route "${route.name}": ${error.toString()}`);
    return [];
  }

  // add redirect to child
  if (children?.length && !vueRoute.redirect) {
    vueRoute.redirect = {
      name: children[0].name
    };
  }

  if (children?.length) {
    const childRoutes = children.flatMap(child => transformElegantRouteToVueRoute(child, layouts, views));

    if(isFirstLevelRoute(route)) {
      vueRoute.children = childRoutes;
    } else {
      vueRoutes.push(...childRoutes);
    }
  }

  vueRoutes.unshift(vueRoute);

  return vueRoutes;
}

/**
 * map of route name and route path
 */
const routeMap: RouteMap = {
  "root": "/",
  "not-found": "/:pathMatch(.*)*",
  "exception": "/exception",
  "exception_403": "/exception/403",
  "exception_404": "/exception/404",
  "exception_500": "/exception/500",
  "403": "/403",
  "404": "/404",
  "500": "/500",
  "business": "/business",
  "business_apartment": "/business/apartment",
  "business_apartment_access": "/business/apartment/access",
  "business_apartment_manage": "/business/apartment/manage",
  "business_apartment_monitor": "/business/apartment/monitor",
  "business_apartment_stranger": "/business/apartment/stranger",
  "business_camera": "/business/camera",
  "business_camera_manage": "/business/camera/manage",
  "business_contract": "/business/contract",
  "business_contract_manage": "/business/contract/manage",
  "business_hazard": "/business/hazard",
  "business_hazard_manage": "/business/hazard/manage",
  "business_hazard_monitor": "/business/hazard/monitor",
  "business_payment": "/business/payment",
  "business_payment_manage": "/business/payment/manage",
  "business_restaurant": "/business/restaurant",
  "business_restaurant_manage": "/business/restaurant/manage",
  "business_restaurant_monitor": "/business/restaurant/monitor",
  "business_sound": "/business/sound",
  "business_sound_corpus": "/business/sound/corpus",
  "business_sound_manage": "/business/sound/manage",
  "business_tenant": "/business/tenant",
  "business_tenant_manage": "/business/tenant/manage",
  "business_vehicle": "/business/vehicle",
  "business_vehicle_manage": "/business/vehicle/manage",
  "business_vehicle_monitor": "/business/vehicle/monitor",
  "demo": "/demo",
  "demo_demo": "/demo/demo",
  "demo_tree": "/demo/tree",
  "home": "/home",
  "iframe-page": "/iframe-page/:url",
  "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
  "monitor": "/monitor",
  "monitor_cache": "/monitor/cache",
  "monitor_logininfor": "/monitor/logininfor",
  "monitor_online": "/monitor/online",
  "monitor_operlog": "/monitor/operlog",
  "social-callback": "/social-callback",
  "system": "/system",
  "system_client": "/system/client",
  "system_config": "/system/config",
  "system_dept": "/system/dept",
  "system_dict": "/system/dict",
  "system_menu": "/system/menu",
  "system_notice": "/system/notice",
  "system_oss": "/system/oss",
  "system_oss-config": "/system/oss-config",
  "system_post": "/system/post",
  "system_role": "/system/role",
  "system_tenant": "/system/tenant",
  "system_tenant-package": "/system/tenant-package",
  "system_user": "/system/user",
  "tool": "/tool",
  "tool_gen": "/tool/gen",
  "user-center": "/user-center"
};

/**
 * get route path by route name
 * @param name route name
 */
export function getRoutePath<T extends RouteKey>(name: T) {
  return routeMap[name];
}

/**
 * get route name by route path
 * @param path route path
 */
export function getRouteName(path: RoutePath) {
  const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

  const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

  return routeName;
}

/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /**
   * namespace Business
   *
   * backend api module: "Business"
   */
  namespace Business {
    /** camera */
    type Camera = Common.CommonRecord<{
      /** 主键，唯一标识每个摄像头信息记录 */
      id: CommonType.IdType;
      /** 摄像头编号（格式：CAM-区域-楼栋-序号，如CAM-A01-1-001） */
      cameraNumber: string;
      /** 摄像头名称（如：1号公寓大堂主摄像头） */
      cameraName: string;
      /** 楼栋号（如1号楼、A栋） */
      buildingNumber: string;
      /** 楼层号（如1层、地下1层） */
      floorNumber: string;
      /** 设备型号（如海康威视DS-2CD3T46WDV3-I3） */
      deviceModel: string;
      /** 安装日期 */
      installDate: string;
      /** 累计报警次数（系统自动更新） */
      alertCount: number;
      /** 关联音柱ID（关联智能音柱信息表） */
      soundColumnId: CommonType.IdType;
      /** 信号强度（如强、中、弱） */
      signalStrength: string;
      /** 摄像头具体位置（如：1号公寓1层大堂入口上方） */
      cameraLocation: string;
      /** 摄像头IP地址（如*************） */
      ipAddress: string;
      /** 登录用户名 */
      loginUsername: string;
      /** 登录密码（建议加密存储） */
      loginPassword: string;
      /** 摄像头状态 */
      cameraStatus: string;
      /** 最后在线时间 */
      lastOnlineTime: string;
      /** 技术参数分辨率（如1920×1080、4K） */
      resolution: string;
      /** 夜视功能 */
      nightVision: string;
      /** 视角范围（如水平90°、垂直60°） */
      viewingAngle: string;
      /** AI功能（多个功能用逗号分隔，如：人脸识别、行为分析、区域入侵检测） */
      aiFunctions: string;
      /** 存储方式（如本地存储、云存储、NVR集中存储） */
      storageMethod: string;
      /** 数据加密状态 */
      dataEncryption: string;
      /** 摄像头拉流地址（RTSP/RTMP协议地址） */
      streamPullUrl: string;
      /** 关于摄像头的备注信息（如：安装高度3.5米、负责区域） */
      remark: string;
      /** 版本号，用于乐观锁控制 */
      version: number;
    }>;

    /** camera search params */
    type CameraSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Camera,
        | 'cameraNumber'
        | 'cameraName'
        | 'buildingNumber'
        | 'floorNumber'
        | 'deviceModel'
        | 'installDate'
        | 'alertCount'
        | 'soundColumnId'
        | 'signalStrength'
        | 'cameraLocation'
        | 'ipAddress'
        | 'loginUsername'
        | 'loginPassword'
        | 'cameraStatus'
        | 'lastOnlineTime'
        | 'resolution'
        | 'nightVision'
        | 'viewingAngle'
        | 'aiFunctions'
        | 'storageMethod'
        | 'dataEncryption'
        | 'streamPullUrl'
      > &
        Api.Common.CommonSearchParams
    >;

    /** camera operate params */
    type CameraOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Camera,
        | 'id'
        | 'cameraNumber'
        | 'cameraName'
        | 'buildingNumber'
        | 'floorNumber'
        | 'deviceModel'
        | 'installDate'
        | 'alertCount'
        | 'soundColumnId'
        | 'signalStrength'
        | 'cameraLocation'
        | 'ipAddress'
        | 'loginUsername'
        | 'loginPassword'
        | 'cameraStatus'
        | 'lastOnlineTime'
        | 'resolution'
        | 'nightVision'
        | 'viewingAngle'
        | 'aiFunctions'
        | 'storageMethod'
        | 'dataEncryption'
        | 'streamPullUrl'
        | 'remark'
      >
    >;

    /** camera list */
    type CameraList = Api.Common.PaginatingQueryRecord<Camera>;
  }
}

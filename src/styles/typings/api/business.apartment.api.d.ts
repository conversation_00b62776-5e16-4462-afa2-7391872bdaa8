/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /**
   * namespace Business
   *
   * backend api module: "Business"
   */
  namespace Business {
    /** apartment */
    type Apartment = Common.CommonRecord<{
      /** 主键，唯一标识每条公寓信息记录 */
      id: CommonType.IdType;
      /** 楼栋号 */
      buildingNumber: string;
      /** 楼层号 */
      floorNumber: string;
      /** 房间号 */
      roomNumber: string;
      /** 户型 */
      houseType: string;
      /** 面积 */
      area: number;
      /** 设施描述 */
      facilities: string;
      /** 房间状态 */
      roomStatus: string;
      /** 可入驻人数限制 */
      occupancyLimit: number;
      /** 主图URL */
      mainImageUrl: string;
      /** 图片URL集合，用逗号分隔 */
      imageUrls: string;
      /** 关于公寓的备注信息 */
      remark: string;
      /** 版本号，用于乐观锁控制 */
      version: number;
    }>;

    /** apartment search params */
    type ApartmentSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Apartment,
        | 'buildingNumber'
        | 'floorNumber'
        | 'roomNumber'
        | 'houseType'
        | 'area'
        | 'facilities'
        | 'roomStatus'
        | 'occupancyLimit'
        | 'mainImageUrl'
        | 'imageUrls'
      > &
        Api.Common.CommonSearchParams
    >;

    /** apartment operate params */
    type ApartmentOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Business.Apartment,
        | 'id'
        | 'buildingNumber'
        | 'floorNumber'
        | 'roomNumber'
        | 'houseType'
        | 'area'
        | 'facilities'
        | 'roomStatus'
        | 'occupancyLimit'
        | 'mainImageUrl'
        | 'imageUrls'
        | 'remark'
      >
    >;

    /** apartment list */
    type ApartmentList = Api.Common.PaginatingQueryRecord<Apartment>;
  }
}

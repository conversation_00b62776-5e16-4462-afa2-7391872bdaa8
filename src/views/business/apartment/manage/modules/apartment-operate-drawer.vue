<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreateApartment, fetchUpdateApartment } from '@/service/api/business/apartment';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'ApartmentOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Apartment | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增用于记录公寓基本信息的',
    edit: '编辑用于记录公寓基本信息的'
  };
  return titles[props.operateType];
});

type Model = Api.Business.ApartmentOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      buildingNumber: '',
      floorNumber: '',
      roomNumber: '',
      houseType: '',
      area: undefined,
      facilities: '',
      roomStatus: '',
      occupancyLimit: undefined,
      mainImageUrl: '',
      imageUrls: '',
      remark: '',
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'buildingNumber'
  | 'floorNumber'
  | 'roomNumber'
  | 'houseType'
  | 'area'
  | 'facilities'
  | 'roomStatus'
  | 'occupancyLimit'
  | 'mainImageUrl'
  | 'imageUrls'
  | 'remark'
  | 'createTime'
  | 'createBy'
  | 'updateTime'
  | 'updateBy'
  | 'version'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  buildingNumber: createRequiredRule('楼栋号不能为空'),
  floorNumber: createRequiredRule('楼层号不能为空'),
  roomNumber: createRequiredRule('房间号不能为空'),
  houseType: createRequiredRule('户型不能为空'),
  area: createRequiredRule('面积不能为空'),
  facilities: createRequiredRule('设施描述不能为空'),
  roomStatus: createRequiredRule('房间状态不能为空'),
  occupancyLimit: createRequiredRule('可入驻人数限制不能为空'),
  mainImageUrl: createRequiredRule('主图URL不能为空'),
  imageUrls: createRequiredRule('图片URL集合，用逗号分隔不能为空'),
  remark: createRequiredRule('关于公寓的备注信息不能为空'),
  createTime: createRequiredRule('记录创建时间不能为空'),
  createBy: createRequiredRule('创建该记录的人员不能为空'),
  updateTime: createRequiredRule('记录更新时间不能为空'),
  updateBy: createRequiredRule('更新该记录的人员不能为空'),
  version: createRequiredRule('版本号，用于乐观锁控制不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, buildingNumber, floorNumber, roomNumber, houseType, area, facilities, roomStatus, occupancyLimit, mainImageUrl, imageUrls, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateApartment({ buildingNumber, floorNumber, roomNumber, houseType, area, facilities, roomStatus, occupancyLimit, mainImageUrl, imageUrls, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateApartment({ id, buildingNumber, floorNumber, roomNumber, houseType, area, facilities, roomStatus, occupancyLimit, mainImageUrl, imageUrls, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="楼栋号" path="buildingNumber">
          <NInput v-model:value="model.buildingNumber" placeholder="请输入楼栋号" />
        </NFormItem>
        <NFormItem label="楼层号" path="floorNumber">
          <NInput v-model:value="model.floorNumber" placeholder="请输入楼层号" />
        </NFormItem>
        <NFormItem label="房间号" path="roomNumber">
          <NInput v-model:value="model.roomNumber" placeholder="请输入房间号" />
        </NFormItem>
        <NFormItem label="户型" path="houseType">
          <NInput v-model:value="model.houseType" placeholder="请输入户型" />
        </NFormItem>
        <NFormItem label="面积" path="area">
          <NInput v-model:value="model.area" placeholder="请输入面积" />
        </NFormItem>
        <NFormItem label="设施描述" path="facilities">
          <NInput
            v-model:value="model.facilities"
            :rows="3"
            type="textarea"
            placeholder="请输入设施描述"
          />
        </NFormItem>
        <NFormItem label="房间状态" path="roomStatus">
          <NInput v-model:value="model.roomStatus" placeholder="请输入房间状态" />
        </NFormItem>
        <NFormItem label="可入驻人数限制" path="occupancyLimit">
          <NInput v-model:value="model.occupancyLimit" placeholder="请输入可入驻人数限制" />
        </NFormItem>
        <NFormItem label="主图URL" path="mainImageUrl">
          <NInput v-model:value="model.mainImageUrl" placeholder="请输入主图URL" />
        </NFormItem>
        <NFormItem label="图片URL集合，用逗号分隔" path="imageUrls">
          <NInput
            v-model:value="model.imageUrls"
            :rows="3"
            type="textarea"
            placeholder="请输入图片URL集合，用逗号分隔"
          />
        </NFormItem>
        <NFormItem label="关于公寓的备注信息" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入关于公寓的备注信息"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'ApartmentSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Business.ApartmentSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="楼栋号" path="buildingNumber" class="pr-24px">
              <NInput v-model:value="model.buildingNumber" placeholder="请输入楼栋号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="楼层号" path="floorNumber" class="pr-24px">
              <NInput v-model:value="model.floorNumber" placeholder="请输入楼层号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="房间号" path="roomNumber" class="pr-24px">
              <NInput v-model:value="model.roomNumber" placeholder="请输入房间号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="户型" path="houseType" class="pr-24px">
              <NSelect
                v-model:value="model.houseType"
                placeholder="请选择户型"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="面积" path="area" class="pr-24px">
              <NInput v-model:value="model.area" placeholder="请输入面积" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="设施描述" path="facilities" class="pr-24px">
              <NInput v-model:value="model.facilities" placeholder="请输入设施描述" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="房间状态" path="roomStatus" class="pr-24px">
              <NSelect
                v-model:value="model.roomStatus"
                placeholder="请选择房间状态"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="可入驻人数限制" path="occupancyLimit" class="pr-24px">
              <NInput v-model:value="model.occupancyLimit" placeholder="请输入可入驻人数限制" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="主图URL" path="mainImageUrl" class="pr-24px">
              <NInput v-model:value="model.mainImageUrl" placeholder="请输入主图URL" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="图片URL集合，用逗号分隔" path="imageUrls" class="pr-24px">
              <NInput v-model:value="model.imageUrls" placeholder="请输入图片URL集合，用逗号分隔" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>

<script setup lang="ts">
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'CameraSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Business.CameraSearchParams>('model', { required: true });

async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="120">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="摄像头编号" path="cameraNumber" class="pr-24px">
              <NInput v-model:value="model.cameraNumber" placeholder="请输入摄像头编号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="摄像头名称" path="cameraName" class="pr-24px">
              <NInput v-model:value="model.cameraName" placeholder="请输入摄像头名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="楼栋号" path="buildingNumber" class="pr-24px">
              <NInput v-model:value="model.buildingNumber" placeholder="请输入楼栋号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="楼层号" path="floorNumber" class="pr-24px">
              <NInput v-model:value="model.floorNumber" placeholder="请输入楼层号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="设备状态" path="cameraStatus" class="pr-24px">
              <NSelect
                v-model:value="model.cameraStatus"
                placeholder="请选择设备状态"
                :options="[
                  { label: '在线', value: 'online' },
                  { label: '离线', value: 'offline' },
                  { label: '维护中', value: 'maintenance' }
                ]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="安装位置" path="cameraLocation" class="pr-24px">
              <NInput v-model:value="model.cameraLocation" placeholder="请输入安装位置" />
            </NFormItemGi>

            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>

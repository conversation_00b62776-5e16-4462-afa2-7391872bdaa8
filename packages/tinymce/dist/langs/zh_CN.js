tinymce.addI18n('zh_CN', {
  '#': '#',
  Accessibility: '\u8F85\u52A9\u529F\u80FD',
  Accordion: '',
  'Accordion body...': '',
  'Accordion summary...': '',
  Action: '\u52A8\u4F5C',
  Activity: '\u6D3B\u52A8',
  Address: '\u5730\u5740',
  Advanced: '\u9AD8\u7EA7',
  Align: '\u5BF9\u9F50',
  'Align center': '\u5C45\u4E2D\u5BF9\u9F50',
  'Align left': '\u5DE6\u5BF9\u9F50',
  'Align right': '\u53F3\u5BF9\u9F50',
  Alignment: '\u5BF9\u9F50',
  'Alignment {0}': '',
  All: '\u5168\u90E8',
  'Alternative description': '\u66FF\u4EE3\u63CF\u8FF0',
  'Alternative source': '\u955C\u50CF',
  'Alternative source URL': '\u66FF\u4EE3\u6765\u6E90\u7F51\u5740',
  Anchor: '\u951A\u70B9',
  'Anchor...': '\u951A\u70B9...',
  Anchors: '\u951A\u70B9',
  'Animals and Nature': '\u52A8\u7269\u548C\u81EA\u7136',
  Arrows: '\u7BAD\u5934',
  B: 'B',
  'Background color': '\u80CC\u666F\u989C\u8272',
  'Background color {0}': '',
  Black: '\u9ED1\u8272',
  Block: '\u5757',
  'Block {0}': '',
  Blockquote: '\u5F15\u6587\u533A\u5757',
  Blocks: '\u6837\u5F0F',
  Blue: '\u84DD\u8272',
  'Blue component': '\u767D\u8272\u90E8\u5206',
  Body: '\u8868\u4F53',
  Bold: '\u7C97\u4F53',
  Border: '\u6846\u7EBF',
  'Border color': '\u6846\u7EBF\u989C\u8272',
  'Border style': '\u8FB9\u6846\u6837\u5F0F',
  'Border width': '\u8FB9\u6846\u5BBD\u5EA6',
  Bottom: '\u4E0B\u65B9\u5BF9\u9F50',
  'Browse files': '',
  'Browse for an image': '\u6D4F\u89C8\u56FE\u50CF',
  'Browse links': '',
  'Bullet list': '\u65E0\u5E8F\u5217\u8868',
  Cancel: '\u53D6\u6D88',
  Caption: '\u6807\u9898',
  Cell: '\u5355\u5143\u683C',
  'Cell padding': '\u5355\u5143\u683C\u5185\u8FB9\u8DDD',
  'Cell properties': '\u5355\u5143\u683C\u5C5E\u6027',
  'Cell spacing': '\u5355\u5143\u683C\u5916\u95F4\u8DDD',
  'Cell styles': '\u5355\u5143\u683C\u6837\u5F0F',
  'Cell type': '\u50A8\u5B58\u683C\u522B',
  Center: '\u5C45\u4E2D',
  Characters: '\u5B57\u7B26',
  'Characters (no spaces)': '\u5B57\u7B26(\u65E0\u7A7A\u683C)',
  Circle: '\u7A7A\u5FC3\u5706',
  Class: '\u7C7B\u578B',
  'Clear formatting': '\u6E05\u9664\u683C\u5F0F',
  Close: '\u5173\u95ED',
  Code: '\u4EE3\u7801',
  'Code sample...': '\u793A\u4F8B\u4EE3\u7801...',
  'Code view': '\u4EE3\u7801\u89C6\u56FE',
  'Color Picker': '\u9009\u8272\u5668',
  'Color swatch': '\u989C\u8272\u6837\u672C',
  Cols: '\u5217',
  Column: '\u5217',
  'Column clipboard actions': '\u5217\u526A\u8D34\u677F\u64CD\u4F5C',
  'Column group': '\u5217\u7EC4',
  'Column header': '\u5217\u6807\u9898',
  'Constrain proportions': '\u4FDD\u6301\u6BD4\u4F8B',
  Copy: '\u590D\u5236',
  'Copy column': '\u590D\u5236\u5217',
  'Copy row': '\u590D\u5236\u884C',
  'Could not find the specified string.': '\u672A\u627E\u5230\u641C\u7D22\u5185\u5BB9\u3002',
  'Could not load emojis': '\u65E0\u6CD5\u52A0\u8F7DEmojis',
  Count: '\u8BA1\u6570',
  Currency: '\u8D27\u5E01',
  'Current window': '\u5F53\u524D\u7A97\u53E3',
  'Custom color': '\u81EA\u5B9A\u4E49\u989C\u8272',
  'Custom...': '\u81EA\u5B9A\u4E49......',
  Cut: '\u526A\u5207',
  'Cut column': '\u526A\u5207\u5217',
  'Cut row': '\u526A\u5207\u884C',
  'Dark Blue': '\u6DF1\u84DD\u8272',
  'Dark Gray': '\u6DF1\u7070\u8272',
  'Dark Green': '\u6DF1\u7EFF\u8272',
  'Dark Orange': '\u6DF1\u6A59\u8272',
  'Dark Purple': '\u6DF1\u7D2B\u8272',
  'Dark Red': '\u6DF1\u7EA2\u8272',
  'Dark Turquoise': '\u6DF1\u84DD\u7EFF\u8272',
  'Dark Yellow': '\u6697\u9EC4\u8272',
  Dashed: '\u865A\u7EBF',
  'Date/time': '\u65E5\u671F/\u65F6\u95F4',
  'Decrease indent': '\u51CF\u5C11\u7F29\u8FDB',
  Default: '\u9884\u8BBE',
  'Delete accordion': '',
  'Delete column': '\u5220\u9664\u5217',
  'Delete row': '\u5220\u9664\u884C',
  'Delete table': '\u5220\u9664\u8868\u683C',
  Dimensions: '\u5C3A\u5BF8',
  Disc: '\u5B9E\u5FC3\u5706',
  Div: 'Div',
  Document: '\u6587\u6863',
  Dotted: '\u865A\u7EBF',
  Double: '\u53CC\u7CBE\u5EA6',
  'Drop an image here': '\u62D6\u653E\u4E00\u5F20\u56FE\u50CF\u81F3\u6B64',
  'Dropped file type is not supported': '\u6B64\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301\u62D6\u653E',
  Edit: '\u7F16\u8F91',
  Embed: '\u5185\u5D4C',
  Emojis: 'Emojis',
  'Emojis...': 'Emojis...',
  Error: '\u9519\u8BEF',
  'Error: Form submit field collision.': '\u9519\u8BEF: \u8868\u5355\u63D0\u4EA4\u5B57\u6BB5\u51B2\u7A81\u3002',
  'Error: No form element found.': '\u9519\u8BEF: \u6CA1\u6709\u8868\u5355\u63A7\u4EF6\u3002',
  'Extended Latin': '\u62C9\u4E01\u8BED\u6269\u5145',
  'Failed to initialize plugin: {0}': '\u63D2\u4EF6\u521D\u59CB\u5316\u5931\u8D25: {0}',
  'Failed to load plugin url: {0}': '\u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25 \u94FE\u63A5: {0}',
  'Failed to load plugin: {0} from url {1}': '\u63D2\u4EF6\u52A0\u8F7D\u5931\u8D25: {0} \u6765\u81EA\u94FE\u63A5 {1}',
  'Failed to upload image: {0}': '\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: {0}',
  File: '\u6587\u4EF6',
  Find: '\u5BFB\u627E',
  'Find (if searchreplace plugin activated)':
    '\u67E5\u627E(\u5982\u679C\u67E5\u627E\u66FF\u6362\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  'Find and Replace': '\u67E5\u627E\u548C\u66FF\u6362',
  'Find and replace...': '\u67E5\u627E\u5E76\u66FF\u6362...',
  'Find in selection': '\u5728\u9009\u533A\u4E2D\u67E5\u627E',
  'Find whole words only': '\u5168\u5B57\u5339\u914D',
  Flags: '\u65D7\u5E1C',
  'Focus to contextual toolbar': '\u79FB\u52A8\u7126\u70B9\u5230\u4E0A\u4E0B\u6587\u83DC\u5355',
  'Focus to element path': '\u79FB\u52A8\u7126\u70B9\u5230\u5143\u7D20\u8DEF\u5F84',
  'Focus to menubar': '\u79FB\u52A8\u7126\u70B9\u5230\u83DC\u5355\u680F',
  'Focus to toolbar': '\u79FB\u52A8\u7126\u70B9\u5230\u5DE5\u5177\u680F',
  Font: '\u5B57\u4F53',
  'Font size {0}': '',
  'Font sizes': '\u5B57\u4F53\u5927\u5C0F',
  'Font {0}': '',
  Fonts: '\u5B57\u4F53',
  'Food and Drink': '\u98DF\u7269\u548C\u996E\u54C1',
  Footer: '\u8868\u5C3E',
  Format: '\u683C\u5F0F',
  'Format {0}': '',
  Formats: '\u683C\u5F0F',
  Fullscreen: '\u5168\u5C4F',
  G: 'G',
  General: '\u4E00\u822C',
  Gray: '\u7070\u8272',
  Green: '\u7EFF\u8272',
  'Green component': '\u7EFF\u8272\u90E8\u5206',
  Groove: '\u51F9\u69FD',
  'Handy Shortcuts': '\u5FEB\u6377\u952E',
  Header: '\u8868\u5934',
  'Header cell': '\u8868\u5934\u5355\u5143\u683C',
  'Heading 1': '\u4E00\u7EA7\u6807\u9898',
  'Heading 2': '\u4E8C\u7EA7\u6807\u9898',
  'Heading 3': '\u4E09\u7EA7\u6807\u9898',
  'Heading 4': '\u56DB\u7EA7\u6807\u9898',
  'Heading 5': '\u4E94\u7EA7\u6807\u9898',
  'Heading 6': '\u516D\u7EA7\u6807\u9898',
  Headings: '\u6807\u9898',
  Height: '\u9AD8\u5EA6',
  Help: '\u5E2E\u52A9',
  'Hex color code': '\u5341\u516D\u8FDB\u5236\u989C\u8272\u4EE3\u7801',
  Hidden: '\u9690\u85CF',
  'Horizontal align': '\u6C34\u5E73\u5BF9\u9F50',
  'Horizontal line': '\u6C34\u5E73\u5206\u5272\u7EBF',
  'Horizontal space': '\u6C34\u5E73\u95F4\u8DDD',
  ID: 'ID',
  'ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.':
    'ID\u5E94\u8BE5\u4EE5\u82F1\u6587\u5B57\u6BCD\u5F00\u5934\uFF0C\u540E\u9762\u53EA\u80FD\u6709\u82F1\u6587\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u7834\u6298\u53F7\u3001\u70B9\u3001\u5192\u53F7\u6216\u4E0B\u5212\u7EBF\u3002',
  'Image is decorative': '\u56FE\u50CF\u662F\u88C5\u9970\u6027\u7684',
  'Image list': '\u56FE\u7247\u6E05\u5355',
  'Image title': '\u56FE\u7247\u6807\u9898',
  'Image...': '\u56FE\u7247...',
  'ImageProxy HTTP error: Could not find Image Proxy':
    '\u56FE\u7247\u4EE3\u7406\u8BF7\u6C42\u9519\u8BEF\uFF1A\u65E0\u6CD5\u627E\u5230\u56FE\u7247\u4EE3\u7406',
  'ImageProxy HTTP error: Incorrect Image Proxy URL':
    '\u56FE\u7247\u4EE3\u7406\u8BF7\u6C42\u9519\u8BEF\uFF1A\u56FE\u7247\u4EE3\u7406\u5730\u5740\u9519\u8BEF',
  'ImageProxy HTTP error: Rejected request':
    '\u56FE\u7247\u4EE3\u7406\u8BF7\u6C42\u9519\u8BEF\uFF1A\u8BF7\u6C42\u88AB\u62D2\u7EDD',
  'ImageProxy HTTP error: Unknown ImageProxy error':
    '\u56FE\u7247\u4EE3\u7406\u8BF7\u6C42\u9519\u8BEF\uFF1A\u672A\u77E5\u7684\u56FE\u7247\u4EE3\u7406\u9519\u8BEF',
  'Increase indent': '\u589E\u52A0\u7F29\u8FDB',
  Inline: '\u6587\u672C',
  Insert: '\u63D2\u5165',
  'Insert Template': '\u63D2\u5165\u6A21\u677F',
  'Insert accordion': '',
  'Insert column after': '\u5728\u53F3\u4FA7\u63D2\u5165\u5217',
  'Insert column before': '\u5728\u5DE6\u4FA7\u63D2\u5165\u5217',
  'Insert date/time': '\u63D2\u5165\u65E5\u671F/\u65F6\u95F4',
  'Insert image': '\u63D2\u5165\u56FE\u7247',
  'Insert link (if link plugin activated)':
    '\u63D2\u5165\u94FE\u63A5 (\u5982\u679C\u94FE\u63A5\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  'Insert row after': '\u5728\u4E0B\u65B9\u63D2\u5165\u884C',
  'Insert row before': '\u5728\u4E0A\u65B9\u63D2\u5165\u884C',
  'Insert table': '\u63D2\u5165\u8868\u683C',
  'Insert template...': '\u63D2\u5165\u6A21\u677F...',
  'Insert video': '\u63D2\u5165\u89C6\u9891',
  'Insert/Edit code sample': '\u63D2\u5165/\u7F16\u8F91\u4EE3\u7801\u793A\u4F8B',
  'Insert/edit image': '\u63D2\u5165/\u7F16\u8F91\u56FE\u7247',
  'Insert/edit link': '\u63D2\u5165/\u7F16\u8F91\u94FE\u63A5',
  'Insert/edit media': '\u63D2\u5165/\u7F16\u8F91\u5A92\u4F53',
  'Insert/edit video': '\u63D2\u5165/\u7F16\u8F91\u89C6\u9891',
  Inset: '\u5D4C\u5165',
  'Invalid hex color code: {0}': '\u5341\u516D\u8FDB\u5236\u989C\u8272\u4EE3\u7801\u65E0\u6548\uFF1A {0}',
  'Invalid input': '\u65E0\u6548\u8F93\u5165',
  Italic: '\u659C\u4F53',
  Justify: '\u4E24\u7AEF\u5BF9\u9F50',
  'Keyboard Navigation': '\u952E\u76D8\u6307\u5F15',
  Language: '\u8BED\u8A00',
  'Learn more...': '\u4E86\u89E3\u66F4\u591A...',
  Left: '\u5DE6',
  'Left to right': '\u7531\u5DE6\u5230\u53F3',
  'Light Blue': '\u6D45\u84DD\u8272',
  'Light Gray': '\u6D45\u7070\u8272',
  'Light Green': '\u6D45\u7EFF\u8272',
  'Light Purple': '\u6D45\u7D2B\u8272',
  'Light Red': '\u6D45\u7EA2\u8272',
  'Light Yellow': '\u6D45\u9EC4\u8272',
  'Line height': '\u884C\u9AD8',
  'Link list': '\u94FE\u63A5\u6E05\u5355',
  'Link...': '\u94FE\u63A5...',
  'List Properties': '\u5217\u8868\u5C5E\u6027',
  'List properties...': '\u6807\u9898\u5B57\u4F53\u5C5E\u6027',
  'Loading emojis...': '\u6B63\u5728\u52A0\u8F7DEmojis...',
  'Loading...': '\u52A0\u8F7D\u4E2D...',
  'Lower Alpha': '\u5C0F\u5199\u82F1\u6587\u5B57\u6BCD',
  'Lower Greek': '\u5C0F\u5199\u5E0C\u814A\u5B57\u6BCD',
  'Lower Roman': '\u5C0F\u5199\u7F57\u9A6C\u6570\u5B57',
  'Match case': '\u5927\u5C0F\u5199\u5339\u914D',
  Mathematical: '\u6570\u5B66',
  'Media poster (Image URL)': '\u5C01\u9762(\u56FE\u7247\u5730\u5740)',
  'Media...': '\u591A\u5A92\u4F53...',
  'Medium Blue': '\u4E2D\u84DD\u8272',
  'Medium Gray': '\u4E2D\u7070\u8272',
  'Medium Purple': '\u4E2D\u7D2B\u8272',
  'Merge cells': '\u5408\u5E76\u5355\u5143\u683C',
  Middle: '\u5C45\u4E2D\u5BF9\u9F50',
  'Midnight Blue': '\u6DF1\u84DD\u8272',
  'More...': '\u66F4\u591A...',
  Name: '\u540D\u79F0',
  'Navy Blue': '\u6D77\u519B\u84DD',
  'New document': '\u65B0\u5EFA\u6587\u6863',
  'New window': '\u65B0\u7A97\u53E3',
  Next: '\u4E0B\u4E00\u4E2A',
  No: '\u5426',
  'No alignment': '\u672A\u5BF9\u9F50',
  'No color': '\u65E0',
  'Nonbreaking space': '\u4E0D\u95F4\u65AD\u7A7A\u683C',
  None: '\u65E0',
  'Numbered list': '\u6709\u5E8F\u5217\u8868',
  OR: '\u6216',
  Objects: '\u7269\u4EF6',
  Ok: '\u786E\u5B9A',
  'Open help dialog': '\u6253\u5F00\u5E2E\u52A9\u5BF9\u8BDD\u6846',
  'Open link': '\u6253\u5F00\u94FE\u63A5',
  'Open link in...': '\u94FE\u63A5\u6253\u5F00\u4F4D\u7F6E...',
  'Open popup menu for split buttons':
    '\u6253\u5F00\u5F39\u51FA\u5F0F\u83DC\u5355\uFF0C\u7528\u4E8E\u62C6\u5206\u6309\u94AE',
  Orange: '\u6A59\u8272',
  Outset: '\u5916\u7F6E',
  'Page break': '\u5206\u9875\u7B26',
  Paragraph: '\u6BB5\u843D',
  Paste: '\u7C98\u8D34',
  'Paste as text': '\u7C98\u8D34\u4E3A\u6587\u672C',
  'Paste column after': '\u7C98\u8D34\u540E\u9762\u7684\u5217',
  'Paste column before': '\u7C98\u8D34\u6B64\u5217\u524D',
  'Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.':
    '\u5F53\u524D\u4E3A\u7EAF\u6587\u672C\u7C98\u8D34\u6A21\u5F0F\uFF0C\u518D\u6B21\u70B9\u51FB\u53EF\u4EE5\u56DE\u5230\u666E\u901A\u7C98\u8D34\u6A21\u5F0F\u3002',
  'Paste or type a link': '\u7C98\u8D34\u6216\u8F93\u5165\u94FE\u63A5',
  'Paste row after': '\u7C98\u8D34\u884C\u5230\u4E0B\u65B9',
  'Paste row before': '\u7C98\u8D34\u884C\u5230\u4E0A\u65B9',
  'Paste your embed code below:': '\u5C06\u5185\u5D4C\u4EE3\u7801\u7C98\u8D34\u5728\u4E0B\u9762:',
  People: '\u4EBA\u7C7B',
  Plugins: '\u63D2\u4EF6',
  'Plugins installed ({0}):': '\u5DF2\u5B89\u88C5\u63D2\u4EF6 ({0}):',
  'Powered by {0}': '\u7531{0}\u9A71\u52A8',
  Pre: '\u524D\u8A00',
  Preferences: '\u9996\u9009\u9879',
  Preformatted: '\u9884\u5148\u683C\u5F0F\u5316\u7684',
  'Premium plugins:': '\u4F18\u79C0\u63D2\u4EF6\uFF1A',
  'Press the Up and Down arrow keys to resize the editor.': '',
  'Press the arrow keys to resize the editor.': '',
  'Press {0} for help': '',
  Preview: '\u9884\u89C8',
  Previous: '\u4E0A\u4E00\u4E2A',
  Print: '\u6253\u5370',
  'Print...': '\u6253\u5370...',
  Purple: '\u7D2B\u8272',
  Quotations: '\u5F15\u7528',
  R: 'R',
  'Range 0 to 255': '\u8303\u56F40\u81F3255',
  Red: '\u7EA2\u8272',
  'Red component': '\u7EA2\u8272\u90E8\u5206',
  Redo: '\u91CD\u505A',
  Remove: '\u79FB\u9664',
  'Remove color': '\u79FB\u9664\u989C\u8272',
  'Remove link': '\u79FB\u9664\u94FE\u63A5',
  Replace: '\u66FF\u6362',
  'Replace all': '\u66FF\u6362\u5168\u90E8',
  'Replace with': '\u66FF\u6362\u4E3A',
  Resize: '\u8C03\u6574\u5927\u5C0F',
  'Restore last draft': '\u6062\u590D\u4E0A\u6B21\u7684\u8349\u7A3F',
  'Reveal or hide additional toolbar items': '',
  'Rich Text Area': '\u5BCC\u6587\u672C\u533A\u57DF',
  'Rich Text Area. Press ALT-0 for help.': '\u7F16\u8F91\u533A\u3002\u6309Alt+0\u952E\u6253\u5F00\u5E2E\u52A9\u3002',
  'Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help':
    '\u7F16\u8F91\u533A\u3002\u6309ALT-F9\u6253\u5F00\u83DC\u5355\uFF0C\u6309ALT-F10\u6253\u5F00\u5DE5\u5177\u680F\uFF0C\u6309ALT-0\u67E5\u770B\u5E2E\u52A9',
  Ridge: '\u6D77\u810A\u5EA7',
  Right: '\u53F3',
  'Right to left': '\u7531\u53F3\u5230\u5DE6',
  Row: '\u884C',
  'Row clipboard actions': '\u884C\u526A\u8D34\u677F\u64CD\u4F5C',
  'Row group': '\u884C\u7EC4',
  'Row header': '\u884C\u5934',
  'Row properties': '\u884C\u5C5E\u6027',
  'Row type': '\u884C\u7C7B\u578B',
  Rows: '\u884C\u6570',
  Save: '\u4FDD\u5B58',
  'Save (if save plugin activated)': '\u4FDD\u5B58(\u5982\u679C\u4FDD\u5B58\u63D2\u4EF6\u5DF2\u6FC0\u6D3B)',
  Scope: '\u8303\u56F4',
  Search: '\u641C\u7D22',
  'Select all': '\u5168\u9009',
  'Select...': '\u9009\u62E9...',
  Selection: '\u9009\u62E9',
  Shortcut: '\u5FEB\u6377\u65B9\u5F0F',
  'Show blocks': '\u663E\u793A\u533A\u5757\u8FB9\u6846',
  'Show caption': '\u663E\u793A\u6807\u9898',
  'Show invisible characters': '\u663E\u793A\u4E0D\u53EF\u89C1\u5B57\u7B26',
  Size: '\u5B57\u53F7',
  Solid: '\u5B9E\u7EBF',
  Source: '\u6E90',
  'Source code': '\u6E90\u4EE3\u7801',
  'Special Character': '\u7279\u6B8A\u5B57\u7B26',
  'Special character...': '\u7279\u6B8A\u5B57\u7B26...',
  'Split cell': '\u62C6\u5206\u5355\u5143\u683C',
  Square: '\u5B9E\u5FC3\u65B9\u5757',
  'Start list at number': '\u4EE5\u6570\u5B57\u5F00\u59CB\u5217\u8868',
  Strikethrough: '\u5220\u9664\u7EBF',
  Style: '\u6837\u5F0F',
  Subscript: '\u4E0B\u6807',
  Superscript: '\u4E0A\u6807',
  'Switch to or from fullscreen mode': '\u5207\u6362\u5168\u5C4F\u6A21\u5F0F',
  Symbols: '\u7B26\u53F7',
  'System Font': '\u7CFB\u7EDF\u5B57\u4F53',
  Table: '\u8868\u683C',
  'Table caption': '\u8868\u683C\u6807\u9898',
  'Table properties': '\u8868\u683C\u5C5E\u6027',
  'Table styles': '\u8868\u683C\u6837\u5F0F',
  Template: '\u6A21\u677F',
  Templates: '\u6A21\u677F',
  Text: '\u6587\u5B57',
  'Text color': '\u6587\u672C\u989C\u8272',
  'Text color {0}': '',
  'Text to display': '\u8981\u663E\u793A\u7684\u6587\u672C',
  'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?':
    '\u4F60\u6240\u586B\u5199\u7684URL\u5730\u5740\u4E3A\u90AE\u4EF6\u5730\u5740\uFF0C\u9700\u8981\u52A0\u4E0Amailto: \u524D\u7F00\u5417\uFF1F',
  'The URL you entered seems to be an external link. Do you want to add the required http:// prefix?':
    '\u4F60\u6240\u586B\u5199\u7684URL\u5730\u5740\u5C5E\u4E8E\u5916\u90E8\u94FE\u63A5\uFF0C\u9700\u8981\u52A0\u4E0Ahttp:// \u524D\u7F00\u5417\uFF1F',
  'The URL you entered seems to be an external link. Do you want to add the required https:// prefix?':
    '\u60A8\u8F93\u5165\u7684 URL \u4F3C\u4E4E\u662F\u4E00\u4E2A\u5916\u90E8\u94FE\u63A5\u3002\u60A8\u60F3\u6DFB\u52A0\u6240\u9700\u7684 https:// \u524D\u7F00\u5417\uFF1F',
  Title: '\u6807\u9898',
  'To open the popup, press Shift+Enter': '\u6309Shitf+Enter\u952E\u6253\u5F00\u5BF9\u8BDD\u6846',
  'Toggle accordion': '',
  Tools: '\u5DE5\u5177',
  Top: '\u4E0A\u65B9\u5BF9\u9F50',
  'Travel and Places': '\u65C5\u6E38\u548C\u5730\u70B9',
  Turquoise: '\u9752\u7EFF\u8272',
  Underline: '\u4E0B\u5212\u7EBF',
  Undo: '\u64A4\u9500',
  Upload: '\u4E0A\u4F20',
  'Uploading image': '\u4E0A\u4F20\u56FE\u7247',
  'Upper Alpha': '\u5927\u5199\u82F1\u6587\u5B57\u6BCD',
  'Upper Roman': '\u5927\u5199\u7F57\u9A6C\u6570\u5B57',
  Url: '\u5730\u5740',
  'User Defined': '\u81EA\u5B9A\u4E49',
  Valid: '\u6709\u6548',
  Version: '\u7248\u672C',
  'Vertical align': '\u5782\u76F4\u5BF9\u9F50',
  'Vertical space': '\u5782\u76F4\u95F4\u8DDD',
  View: '\u67E5\u770B',
  'Visual aids': '\u7F51\u683C\u7EBF',
  Warn: '\u8B66\u544A',
  White: '\u767D\u8272',
  Width: '\u5BBD\u5EA6',
  'Word count': '\u5B57\u6570',
  Words: '\u5355\u8BCD',
  'Words: {0}': '\u5B57\u6570\uFF1A{0}',
  Yellow: '\u9EC4\u8272',
  Yes: '\u662F',
  'You are using {0}': '\u4F60\u6B63\u5728\u4F7F\u7528 {0}',
  'You have unsaved changes are you sure you want to navigate away?':
    '\u4F60\u8FD8\u6709\u6587\u6863\u5C1A\u672A\u4FDD\u5B58\uFF0C\u786E\u5B9A\u8981\u79BB\u5F00\uFF1F',
  "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":
    '\u4F60\u7684\u6D4F\u89C8\u5668\u4E0D\u652F\u6301\u6253\u5F00\u526A\u8D34\u677F\uFF0C\u8BF7\u4F7F\u7528Ctrl+X/C/V\u7B49\u5FEB\u6377\u952E\u3002',
  alignment: '\u5BF9\u9F50',
  'austral sign': '\u6FB3\u5143\u7B26\u53F7',
  'cedi sign': '\u585E\u5730\u7B26\u53F7',
  'colon sign': '\u5192\u53F7',
  'cruzeiro sign': '\u514B\u9C81\u8D5B\u7F57\u5E01\u7B26\u53F7',
  'currency sign': '\u8D27\u5E01\u7B26\u53F7',
  'dollar sign': '\u7F8E\u5143\u7B26\u53F7',
  'dong sign': '\u8D8A\u5357\u76FE\u7B26\u53F7',
  'drachma sign': '\u5FB7\u62C9\u514B\u9A6C\u7B26\u53F7',
  'euro-currency sign': '\u6B27\u5143\u7B26\u53F7',
  example: '\u793A\u4F8B',
  formatting: '\u683C\u5F0F\u5316',
  'french franc sign': '\u6CD5\u90CE\u7B26\u53F7',
  'german penny symbol': '\u5FB7\u56FD\u4FBF\u58EB\u7B26\u53F7',
  'guarani sign': '\u74DC\u62C9\u5C3C\u7B26\u53F7',
  history: '\u5386\u53F2',
  'hryvnia sign': '\u683C\u91CC\u592B\u5C3C\u4E9A\u7B26\u53F7',
  indentation: '\u7F29\u8FDB',
  'indian rupee sign': '\u5370\u5EA6\u5362\u6BD4',
  'kip sign': '\u8001\u631D\u57FA\u666E\u7B26\u53F7',
  'lira sign': '\u91CC\u62C9\u7B26\u53F7',
  'livre tournois sign': '\u91CC\u5F17\u5F17\u5C14\u7B26\u53F7',
  'manat sign': '\u9A6C\u7EB3\u7279\u7B26\u53F7',
  'mill sign': '\u5BC6\u5C14\u7B26\u53F7',
  'naira sign': '\u5948\u62C9\u7B26\u53F7',
  'new sheqel sign': '\u65B0\u8C22\u514B\u5C14\u7B26\u53F7',
  'nordic mark sign': '\u5317\u6B27\u9A6C\u514B',
  'peseta sign': '\u6BD4\u585E\u5854\u7B26\u53F7',
  'peso sign': '\u6BD4\u7D22\u7B26\u53F7',
  'ruble sign': '\u5362\u5E03\u7B26\u53F7',
  'rupee sign': '\u5362\u6BD4\u7B26\u53F7',
  'spesmilo sign': 'spesmilo\u7B26\u53F7',
  styles: '\u6837\u5F0F',
  'tenge sign': '\u575A\u6208\u7B26\u53F7',
  'tugrik sign': '\u56FE\u683C\u91CC\u514B\u7B26\u53F7',
  'turkish lira sign': '\u571F\u8033\u5176\u91CC\u62C9',
  'won sign': '\u97E9\u5143\u7B26\u53F7',
  'yen character': '\u65E5\u5143\u5B57\u6837',
  'yen/yuan character variant one': '\u5143\u5B57\u6837\uFF08\u5927\u5199\uFF09',
  'yuan character': '\u4EBA\u6C11\u5E01\u5143\u5B57\u6837',
  'yuan character, in hong kong and taiwan': '\u5143\u5B57\u6837\uFF08\u6E2F\u53F0\u5730\u533A\uFF09',
  '{0} characters': '{0} \u4E2A\u5B57\u7B26',
  '{0} columns, {1} rows': '',
  '{0} words': '{0} \u5B57'
});

tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.zh_TW',
  '<h1>開始鍵盤瀏覽</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>跳至功能表列</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F9</dd>\n' +
    '  <dd>macOS：&#x2325;F9</dd>\n' +
    '  <dt>跳至工具列</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F10</dd>\n' +
    '  <dd>macOS：&#x2325;F10</dd>\n' +
    '  <dt>跳至頁尾</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F11</dd>\n' +
    '  <dd>macOS：&#x2325;F11</dd>\n' +
    '  <dt>跳至通知</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F12</dd>\n' +
    '  <dd>macOS：&#x2325;F12</dd>\n' +
    '  <dt>跳至關聯式工具列</dt>\n' +
    '  <dd>Windows、Linux 或 macOS：Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>瀏覽會從第一個 UI 項目開始，該項目會反白顯示，但如果是「頁尾」元素路徑的第一項，\n' +
    '  則加底線。</p>\n' +
    '\n' +
    '<h1>在 UI 區段之間瀏覽</h1>\n' +
    '\n' +
    '<p>從 UI 區段移至下一個，請按 <strong>Tab</strong>。</p>\n' +
    '\n' +
    '<p>從 UI 區段移回上一個，請按 <strong>Shift+Tab</strong>。</p>\n' +
    '\n' +
    '<p>這些 UI 區段的 <strong>Tab</strong> 順序如下：</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>功能表列</li>\n' +
    '  <li>各個工具列群組</li>\n' +
    '  <li>側邊欄</li>\n' +
    '  <li>頁尾中的元素路徑</li>\n' +
    '  <li>頁尾中字數切換按鈕</li>\n' +
    '  <li>頁尾中的品牌連結</li>\n' +
    '  <li>頁尾中編輯器調整大小控點</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>如果 UI 區段未顯示，表示已略過該區段。</p>\n' +
    '\n' +
    '<p>如果鍵盤瀏覽跳至頁尾，但沒有顯示側邊欄，則按下 <strong>Shift+Tab</strong>\n' +
    '  會跳至第一個工具列群組，而不是最後一個。</p>\n' +
    '\n' +
    '<h1>在 UI 區段之內瀏覽</h1>\n' +
    '\n' +
    '<p>在兩個 UI 元素之間移動，請按適當的<strong>方向</strong>鍵。</p>\n' +
    '\n' +
    '<p><strong>向左</strong>和<strong>向右</strong>方向鍵</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>在功能表列中的功能表之間移動。</li>\n' +
    '  <li>開啟功能表中的子功能表。</li>\n' +
    '  <li>在工具列群組中的按鈕之間移動。</li>\n' +
    '  <li>在頁尾的元素路徑中項目之間移動。</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>向下</strong>和<strong>向上</strong>方向鍵</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>在功能表中的功能表項目之間移動。</li>\n' +
    '  <li>在工具列快顯功能表中的項目之間移動。</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>方向</strong>鍵會在所跳至 UI 區段之內循環。</p>\n' +
    '\n' +
    '<p>若要關閉已開啟的功能表、已開啟的子功能表，或已開啟的快顯功能表，請按 <strong>Esc</strong> 鍵。</p>\n' +
    '\n' +
    '<p>如果目前已跳至特定 UI 區段的「頂端」，則按 <strong>Esc</strong> 鍵也會結束\n' +
    '  整個鍵盤瀏覽。</p>\n' +
    '\n' +
    '<h1>執行功能表列項目或工具列按鈕</h1>\n' +
    '\n' +
    '<p>當想要的功能表項目或工具列按鈕已反白顯示時，按 <strong>Return</strong>、<strong>Enter</strong>、\n' +
    '  或<strong>空白鍵</strong>即可執行該項目。</p>\n' +
    '\n' +
    '<h1>瀏覽非索引標籤式對話方塊</h1>\n' +
    '\n' +
    '<p>在非索引標籤式對話方塊中，開啟對話方塊時會跳至第一個互動元件。</p>\n' +
    '\n' +
    '<p>按 <strong>Tab</strong> 或 <strong>Shift+Tab</strong> 即可在互動式對話方塊元件之間瀏覽。</p>\n' +
    '\n' +
    '<h1>瀏覽索引標籤式對話方塊</h1>\n' +
    '\n' +
    '<p>在索引標籤式對話方塊中，開啟對話方塊時會跳至索引標籤式功能表中的第一個按鈕。</p>\n' +
    '\n' +
    '<p>若要在此對話方塊的互動式元件之間瀏覽，請按 <strong>Tab</strong> 或\n' +
    '  <strong>Shift+Tab</strong>。</p>\n' +
    '\n' +
    '<p>先跳至索引標籤式功能表，然後按適當的<strong>方向</strong>鍵，即可切換至另一個對話方塊索引標籤，\n' +
    '  以循環瀏覽可用的索引標籤。</p>\n'
);

tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.pt_PT',
  '<h1>Iniciar navegação com teclado</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Foco na barra de menu</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Foco na barra de ferramentas</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Foco no rodapé</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Focar a notificação</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Foco numa barra de ferramentas contextual</dt>\n' +
    '  <dd>Windows, Linux ou macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>A navegação começará no primeiro item de IU, que estará realçado ou sublinhado, no caso do primeiro item no\n' +
    '  caminho do elemento do rodapé.</p>\n' +
    '\n' +
    '<h1>Navegar entre secções de IU</h1>\n' +
    '\n' +
    '<p>Para se mover de uma secção de IU para a seguinte, prima <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>Para se mover de uma secção de IU para a anterior, prima <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>A ordem de <strong>tabulação</strong> destas secções de IU é:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Barra de menu</li>\n' +
    '  <li>Cada grupo da barra de ferramentas</li>\n' +
    '  <li>Barra lateral</li>\n' +
    '  <li>Caminho do elemento no rodapé</li>\n' +
    '  <li>Botão de alternar da contagem de palavras no rodapé</li>\n' +
    '  <li>Ligação da marca no rodapé</li>\n' +
    '  <li>Alça de redimensionamento do editor no rodapé</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>Se uma secção de IU não estiver presente, é ignorada.</p>\n' +
    '\n' +
    '<p>Se o rodapé tiver foco de navegação com teclado e não existir uma barra lateral visível, premir <strong>Shift+Tab</strong>\n' +
    '  move o foco para o primeiro grupo da barra de ferramentas e não para o último.</p>\n' +
    '\n' +
    '<h1>Navegar nas secções de IU</h1>\n' +
    '\n' +
    '<p>Para se mover de um elemento de IU para o seguinte, prima a tecla de <strong>seta</strong> adequada.</p>\n' +
    '\n' +
    '<p>As teclas de seta <strong>Para a esquerda</strong> e <strong>Para a direita</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>movem-se entre menus na barra de menu.</li>\n' +
    '  <li>abrem um submenu num menu.</li>\n' +
    '  <li>movem-se entre botões num grupo da barra de ferramentas.</li>\n' +
    '  <li>movem-se entre itens no caminho do elemento do rodapé.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>As teclas de seta <strong>Para cima</strong> e <strong>Para baixo</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>movem-se entre itens de menu num menu.</li>\n' +
    '  <li>movem-se entre itens num menu de pop-up da barra de ferramentas.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>As teclas de <strong>seta</strong> deslocam-se ciclicamente na secção de IU em foco.</p>\n' +
    '\n' +
    '<p>Para fechar um menu aberto, um submenu aberto ou um menu de pop-up aberto, prima a tecla <strong>Esc</strong>.</p>\n' +
    '\n' +
    '<p>Se o foco atual estiver no "topo" de determinada secção de IU, premir a tecla <strong>Esc</strong> também fecha\n' +
    '  completamente a navegação com teclado.</p>\n' +
    '\n' +
    '<h1>Executar um item de menu ou botão da barra de ferramentas</h1>\n' +
    '\n' +
    '<p>Quando o item de menu ou o botão da barra de ferramentas pretendido estiver realçado, prima <strong>Retrocesso</strong>, <strong>Enter</strong>\n' +
    '  ou a <strong>Barra de espaço</strong> para executar o item.</p>\n' +
    '\n' +
    '<h1>Navegar em diálogos sem separadores</h1>\n' +
    '\n' +
    '<p>Nos diálogos sem separadores, o primeiro componente interativo fica em foco quando o diálogo abre.</p>\n' +
    '\n' +
    '<p>Navegue entre componentes interativos do diálogo, premindo <strong>Tab</strong> ou <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Navegar em diálogos com separadores</h1>\n' +
    '\n' +
    '<p>Nos diálogos com separadores, o primeiro botão no menu do separador fica em foco quando o diálogo abre.</p>\n' +
    '\n' +
    '<p>Navegue entre os componentes interativos deste separador do diálogo, premindo <strong>Tab</strong> ou\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Mude para outro separador do diálogo colocando o menu do separador em foco e, em seguida, premindo a tecla de <strong>seta</strong>\n' +
    '  adequada para se deslocar ciclicamente pelos separadores disponíveis.</p>\n'
);

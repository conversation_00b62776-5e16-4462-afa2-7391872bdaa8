tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.zh_CN',
  '<h1>开始键盘导航</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>使菜单栏处于焦点</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F9</dd>\n' +
    '  <dd>macOS：&#x2325;F9</dd>\n' +
    '  <dt>使工具栏处于焦点</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F10</dd>\n' +
    '  <dd>macOS：&#x2325;F10</dd>\n' +
    '  <dt>使页脚处于焦点</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F11</dd>\n' +
    '  <dd>macOS：&#x2325;F11</dd>\n' +
    '  <dt>使通知处于焦点</dt>\n' +
    '  <dd>Windows 或 Linux：Alt+F12</dd>\n' +
    '  <dd>macOS：&#x2325;F12</dd>\n' +
    '  <dt>使上下文工具栏处于焦点</dt>\n' +
    '  <dd>Windows、Linux 或 macOS：Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>导航将在第一个 UI 项上开始，其中突出显示该项，或者对于页脚元素路径中的第一项，将为其添加下划线。</p>\n' +
    '\n' +
    '<h1>在 UI 部分之间导航</h1>\n' +
    '\n' +
    '<p>要从一个 UI 部分移至下一个，请按 <strong>Tab</strong>。</p>\n' +
    '\n' +
    '<p>要从一个 UI 部分移至上一个，请按 <strong>Shift+Tab</strong>。</p>\n' +
    '\n' +
    '<p>这些 UI 部分的 <strong>Tab</strong> 顺序为：</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>菜单栏</li>\n' +
    '  <li>每个工具栏组</li>\n' +
    '  <li>边栏</li>\n' +
    '  <li>页脚中的元素路径</li>\n' +
    '  <li>页脚中的字数切换按钮</li>\n' +
    '  <li>页脚中的品牌链接</li>\n' +
    '  <li>页脚中的编辑器调整大小图柄</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>如果不存在某个 UI 部分，则跳过它。</p>\n' +
    '\n' +
    '<p>如果键盘导航焦点在页脚，并且没有可见的边栏，则按 <strong>Shift+Tab</strong> 将焦点移至第一个工具栏组而非最后一个。</p>\n' +
    '\n' +
    '<h1>在 UI 部分内导航</h1>\n' +
    '\n' +
    '<p>要从一个 UI 元素移至下一个，请按相应的<strong>箭头</strong>键。</p>\n' +
    '\n' +
    '<p><strong>左</strong>和<strong>右</strong>箭头键</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>在菜单栏中的菜单之间移动。</li>\n' +
    '  <li>打开菜单中的子菜单。</li>\n' +
    '  <li>在工具栏组中的按钮之间移动。</li>\n' +
    '  <li>在页脚的元素路径中的各项之间移动。</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>下</strong>和<strong>上</strong>箭头键</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>在菜单中的菜单项之间移动。</li>\n' +
    '  <li>在工具栏弹出菜单中的各项之间移动。</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>箭头</strong>键在具有焦点的 UI 部分内循环。</p>\n' +
    '\n' +
    '<p>要关闭打开的菜单、打开的子菜单或打开的弹出菜单，请按 <strong>Esc</strong> 键。</p>\n' +
    '\n' +
    '<p>如果当前的焦点在特定 UI 部分的“顶部”，则按 <strong>Esc</strong> 键还将完全退出键盘导航。</p>\n' +
    '\n' +
    '<h1>执行菜单项或工具栏按钮</h1>\n' +
    '\n' +
    '<p>当突出显示所需的菜单项或工具栏按钮时，按 <strong>Return</strong>、<strong>Enter</strong> 或<strong>空格</strong>以执行该项。</p>\n' +
    '\n' +
    '<h1>在非标签页式对话框中导航</h1>\n' +
    '\n' +
    '<p>在非标签页式对话框中，当对话框打开时，第一个交互组件获得焦点。</p>\n' +
    '\n' +
    '<p>通过按 <strong>Tab</strong> 或 <strong>Shift+Tab</strong>，在交互对话框组件之间导航。</p>\n' +
    '\n' +
    '<h1>在标签页式对话框中导航</h1>\n' +
    '\n' +
    '<p>在标签页式对话框中，当对话框打开时，标签页菜单中的第一个按钮获得焦点。</p>\n' +
    '\n' +
    '<p>通过按 <strong>Tab</strong> 或 <strong>Shift+Tab</strong>，在此对话框的交互组件之间导航。</p>\n' +
    '\n' +
    '<p>通过将焦点移至另一对话框标签页的菜单，然后按相应的<strong>箭头</strong>键以在可用的标签页间循环，从而切换到该对话框标签页。</p>\n'
);

tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.en',
  '<h1>Begin keyboard navigation</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Focus the Menu bar</dt>\n' +
    '  <dd>Windows or Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Focus the Toolbar</dt>\n' +
    '  <dd>Windows or Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Focus the footer</dt>\n' +
    '  <dd>Windows or Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Focus the notification</dt>\n' +
    '  <dd>Windows or Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Focus a contextual toolbar</dt>\n' +
    '  <dd>Windows, Linux or macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>Navigation will start at the first UI item, which will be highlighted, or underlined in the case of the first item in\n' +
    '  the Footer element path.</p>\n' +
    '\n' +
    '<h1>Navigate between UI sections</h1>\n' +
    '\n' +
    '<p>To move from one UI section to the next, press <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>To move from one UI section to the previous, press <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>The <strong>Tab</strong> order of these UI sections is:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Menu bar</li>\n' +
    '  <li>Each toolbar group</li>\n' +
    '  <li>Sidebar</li>\n' +
    '  <li>Element path in the footer</li>\n' +
    '  <li>Word count toggle button in the footer</li>\n' +
    '  <li>Branding link in the footer</li>\n' +
    '  <li>Editor resize handle in the footer</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>If a UI section is not present, it is skipped.</p>\n' +
    '\n' +
    '<p>If the footer has keyboard navigation focus, and there is no visible sidebar, pressing <strong>Shift+Tab</strong>\n' +
    '  moves focus to the first toolbar group, not the last.</p>\n' +
    '\n' +
    '<h1>Navigate within UI sections</h1>\n' +
    '\n' +
    '<p>To move from one UI element to the next, press the appropriate <strong>Arrow</strong> key.</p>\n' +
    '\n' +
    '<p>The <strong>Left</strong> and <strong>Right</strong> arrow keys</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>move between menus in the menu bar.</li>\n' +
    '  <li>open a sub-menu in a menu.</li>\n' +
    '  <li>move between buttons in a toolbar group.</li>\n' +
    '  <li>move between items in the footer’s element path.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>The <strong>Down</strong> and <strong>Up</strong> arrow keys</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>move between menu items in a menu.</li>\n' +
    '  <li>move between items in a toolbar pop-up menu.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>Arrow</strong> keys cycle within the focused UI section.</p>\n' +
    '\n' +
    '<p>To close an open menu, an open sub-menu, or an open pop-up menu, press the <strong>Esc</strong> key.</p>\n' +
    '\n' +
    '<p>If the current focus is at the ‘top’ of a particular UI section, pressing the <strong>Esc</strong> key also exits\n' +
    '  keyboard navigation entirely.</p>\n' +
    '\n' +
    '<h1>Execute a menu item or toolbar button</h1>\n' +
    '\n' +
    '<p>When the desired menu item or toolbar button is highlighted, press <strong>Return</strong>, <strong>Enter</strong>,\n' +
    '  or the <strong>Space bar</strong> to execute the item.</p>\n' +
    '\n' +
    '<h1>Navigate non-tabbed dialogs</h1>\n' +
    '\n' +
    '<p>In non-tabbed dialogs, the first interactive component takes focus when the dialog opens.</p>\n' +
    '\n' +
    '<p>Navigate between interactive dialog components by pressing <strong>Tab</strong> or <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Navigate tabbed dialogs</h1>\n' +
    '\n' +
    '<p>In tabbed dialogs, the first button in the tab menu takes focus when the dialog opens.</p>\n' +
    '\n' +
    '<p>Navigate between interactive components of this dialog tab by pressing <strong>Tab</strong> or\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Switch to another dialog tab by giving the tab menu focus and then pressing the appropriate <strong>Arrow</strong>\n' +
    '  key to cycle through the available tabs.</p>\n'
);

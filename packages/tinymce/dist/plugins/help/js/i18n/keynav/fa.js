tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.fa',
  '<h1>شروع پیمایش صفحه‌کلید</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>تمرکز بر نوار منو</dt>\n' +
    '  <dd>Windows یا Linux:‎‏: Alt+F9</dd>\n' +
    '  <dd>‎‏macOS: &#x2325;F9‎‏</dd>\n' +
    '  <dt>تمرکز بر نوار ابزار</dt>\n' +
    '  <dd>Windows یا Linux‎‏: Alt+F10</dd>\n' +
    '  <dd>‎‏macOS: &#x2325;F10‎‏</dd>\n' +
    '  <dt>تمرکز بر پانویس</dt>\n' +
    '  <dd>Windows یا Linux‎‏: Alt+F11</dd>\n' +
    '  <dd>‎‏macOS: &#x2325;F11‎‏</dd>\n' +
    '  <dt>تمرکز اعلان</dt>\n' +
    '  <dd>ویندوز یا لینوکس: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>تمرکز بر نوار ابزار بافتاری</dt>\n' +
    '  <dd>Windows ،Linux یا macOS:‏ Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>پیمایش در اولین مورد رابط کاربری شروع می‌شود و درخصوص اولین مورد در\n' +
    '  مسیر عنصر پانویس، برجسته یا زیرخط‌دار می‌شود.</p>\n' +
    '\n' +
    '<h1>پیمایش بین بخش‌های رابط کاربری</h1>\n' +
    '\n' +
    '<p>برای جابجایی از یک بخش رابط کاربری به بخش بعدی، <strong>Tab</strong> را فشار دهید.</p>\n' +
    '\n' +
    '<p>برای جابجایی از یک بخش رابط کاربری به بخش قبلی، <strong>Shift+Tab</strong> را فشار دهید.</p>\n' +
    '\n' +
    '<p>ترتیب <strong>Tab</strong> این بخش‌های رابط کاربری عبارتند از:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>نوار منو</li>\n' +
    '  <li>هر گروه نوار ابزار</li>\n' +
    '  <li>نوار کناری</li>\n' +
    '  <li>مسیر عنصر در پانویس</li>\n' +
    '  <li>دکمه تغییر وضعیت تعداد کلمات در پانویس</li>\n' +
    '  <li>پیوند نمانام‌سازی در پانویس</li>\n' +
    '  <li>دسته تغییر اندازه ویرایشگر در پانویس</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>اگر بخشی از رابط کاربری موجود نباشد، رد می‌شود.</p>\n' +
    '\n' +
    '<p>اگر پانویس دارای تمرکز بر پیمایش صفحه‌کلید باشد،‌ و نوار کناری قابل‌مشاهده وجود ندارد، فشردن <strong>Shift+Tab</strong>\n' +
    '  تمرکز را به گروه نوار ابزار اول می‌برد، نه آخر.</p>\n' +
    '\n' +
    '<h1>پیمایش در بخش‌های رابط کاربری</h1>\n' +
    '\n' +
    '<p>برای جابجایی از یک عنصر رابط کاربری به بعدی، کلید <strong>جهت‌نمای</strong> مناسب را فشار دهید.</p>\n' +
    '\n' +
    '<p>کلیدهای جهت‌نمای <strong>چپ</strong> و <strong>راست</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>جابجایی بین منوها در نوار منو.</li>\n' +
    '  <li>باز کردن منوی فرعی در یک منو.</li>\n' +
    '  <li>جابجایی بین دکمه‌ها در یک گروه نوار ابزار.</li>\n' +
    '  <li>جابجایی بین موارد در مسیر عنصر پانویس.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>کلیدهای جهت‌نمای <strong>پایین</strong> و <strong>بالا</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>جابجایی بین موارد منو در یک منو.</li>\n' +
    '  <li>جابجایی بین موارد در یک منوی بازشوی نوار ابزار.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>کلیدهای<strong>جهت‌نما</strong> در بخش رابط کاربری متمرکز می‌چرخند.</p>\n' +
    '\n' +
    '<p>برای بستن یک منوی باز، یک منوی فرعی باز، یا یک منوی بازشوی باز، کلید <strong>Esc</strong> را فشار دهید.</p>\n' +
    '\n' +
    '<p>اگر تمرکز فعلی در «بالای» یک بخش رابط کاربری خاص است، فشردن کلید <strong>Esc</strong> نیز موجب\n' +
    '  خروج کامل از پیمایش صفحه‌کلید می‌شود.</p>\n' +
    '\n' +
    '<h1>اجرای یک مورد منو یا دکمه نوار ابزار</h1>\n' +
    '\n' +
    '<p>وقتی مورد منو یا دکمه نوار ابزار مورد نظر هایلایت شد، دکمه <strong>بازگشت</strong>، <strong>Enter</strong>،\n' +
    '  یا <strong>نوار Space</strong> را فشار دهید تا مورد را اجرا کنید.</p>\n' +
    '\n' +
    '<h1>پیمایش در کادرهای گفتگوی بدون زبانه</h1>\n' +
    '\n' +
    '<p>در کادرهای گفتگوی بدون زبانه، وقتی کادر گفتگو باز می‌شود، اولین جزء تعاملی متمرکز می‌شود.</p>\n' +
    '\n' +
    '<p>با فشردن <strong>Tab</strong> یا <strong>Shift+Tab</strong>، بین اجزای کادر گفتگوی تعاملی پیمایش کنید.</p>\n' +
    '\n' +
    '<h1>پیمایش کادرهای گفتگوی زبانه‌دار</h1>\n' +
    '\n' +
    '<p>در کادرهای گفتگوی زبانه‌دار، وقتی کادر گفتگو باز می‌شود، اولین دکمه در منوی زبانه متمرکز می‌شود.</p>\n' +
    '\n' +
    '<p>با فشردن <strong>Tab</strong> یا\n' +
    '  <strong>Shift+Tab</strong>، بین اجزای تعاملی این زبانه کادر گفتگو پیمایش کنید.</p>\n' +
    '\n' +
    '<p>با دادن تمرکز به منوی زبانه و سپس فشار دادن کلید <strong>جهت‌نمای</strong>\n' +
    '  مناسب برای چرخش میان زبانه‌های موجود، به زبانه کادر گفتگوی دیگری بروید.</p>\n'
);

tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.pt_BR',
  '<h1>Iniciar navegação pelo teclado</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Foco na barra de menus</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Foco na barra de ferramentas</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Foco no rodapé</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Foco na notificação</dt>\n' +
    '  <dd>Windows ou Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Foco na barra de ferramentas contextual</dt>\n' +
    '  <dd>Windows, Linux ou macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>A navegação inicia no primeiro item da IU, que será destacado ou sublinhado no caso do primeiro item no\n' +
    '  caminho do elemento Rodapé.</p>\n' +
    '\n' +
    '<h1>Navegar entre seções da IU</h1>\n' +
    '\n' +
    '<p>Para ir de uma seção da IU para a seguinte, pressione <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>Para ir de uma seção da IU para a anterior, pressione <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>A ordem de <strong>Tab</strong> destas seções da IU é:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Barra de menus</li>\n' +
    '  <li>Cada grupo da barra de ferramentas</li>\n' +
    '  <li>Barra lateral</li>\n' +
    '  <li>Caminho do elemento no rodapé</li>\n' +
    '  <li>Botão de alternar contagem de palavras no rodapé</li>\n' +
    '  <li>Link da marca no rodapé</li>\n' +
    '  <li>Alça de redimensionamento do editor no rodapé</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>Se não houver uma seção da IU, ela será pulada.</p>\n' +
    '\n' +
    '<p>Se o rodapé tiver o foco da navegação pelo teclado e não houver uma barra lateral visível, pressionar <strong>Shift+Tab</strong>\n' +
    '  move o foco para o primeiro grupo da barra de ferramentas, não para o último.</p>\n' +
    '\n' +
    '<h1>Navegar dentro das seções da IU</h1>\n' +
    '\n' +
    '<p>Para ir de um elemento da IU para o seguinte, pressione a <strong>Seta</strong> correspondente.</p>\n' +
    '\n' +
    '<p>As teclas de seta <strong>Esquerda</strong> e <strong>Direita</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>movem entre menus na barra de menus.</li>\n' +
    '  <li>abrem um submenu em um menu.</li>\n' +
    '  <li>movem entre botões em um grupo da barra de ferramentas.</li>\n' +
    '  <li>movem entre itens no caminho do elemento do rodapé.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>As teclas de seta <strong>Abaixo</strong> e <strong>Acima</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>movem entre itens de menu em um menu.</li>\n' +
    '  <li>movem entre itens em um menu suspenso da barra de ferramentas.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>As teclas de <strong>Seta</strong> alternam dentre a seção da IU em foco.</p>\n' +
    '\n' +
    '<p>Para fechar um menu aberto, um submenu aberto ou um menu suspenso aberto, pressione <strong>Esc</strong>.</p>\n' +
    '\n' +
    '<p>Se o foco atual estiver no ‘alto’ de determinada seção da IU, pressionar <strong>Esc</strong> também sai\n' +
    '  totalmente da navegação pelo teclado.</p>\n' +
    '\n' +
    '<h1>Executar um item de menu ou botão da barra de ferramentas</h1>\n' +
    '\n' +
    '<p>Com o item de menu ou botão da barra de ferramentas desejado destacado, pressione <strong>Return</strong>, <strong>Enter</strong>,\n' +
    '  ou a <strong>Barra de espaço</strong> para executar o item.</p>\n' +
    '\n' +
    '<h1>Navegar por caixas de diálogo sem guias</h1>\n' +
    '\n' +
    '<p>Em caixas de diálogo sem guias, o primeiro componente interativo recebe o foco quando a caixa de diálogo abre.</p>\n' +
    '\n' +
    '<p>Navegue entre componentes interativos de caixa de diálogo pressionando <strong>Tab</strong> ou <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Navegar por caixas de diálogo com guias</h1>\n' +
    '\n' +
    '<p>Em caixas de diálogo com guias, o primeiro botão no menu da guia recebe o foco quando a caixa de diálogo abre.</p>\n' +
    '\n' +
    '<p>Navegue entre componentes interativos dessa guia da caixa de diálogo pressionando <strong>Tab</strong> ou\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Alterne para outra guia da caixa de diálogo colocando o foco no menu da guia e pressionando a <strong>Seta</strong>\n' +
    '  adequada para percorrer as guias disponíveis.</p>\n'
);

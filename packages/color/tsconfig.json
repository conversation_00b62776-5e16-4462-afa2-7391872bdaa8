{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "types": ["node"], "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}